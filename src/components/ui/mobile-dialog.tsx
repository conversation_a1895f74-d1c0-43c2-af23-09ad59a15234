"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { useMediaQuery } from "@/hooks/use-media-query";
import { useSwipeToDismiss } from "@/hooks/use-swipe-to-dismiss";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Sheet,
  SheetDes<PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  SheetTitle,
} from "@/components/ui/sheet";
import * as SheetPrimitive from "@radix-ui/react-dialog";
import { cn } from "@/lib/utils";

interface MobileDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  children: React.ReactNode;
}

// Context to pass onOpenChange to MobileDialogContent
const MobileDialogContext = React.createContext<{
  onOpenChange?: (open: boolean) => void;
}>({});

interface MobileDialogContentProps {
  className?: string;
  children: React.ReactNode;
  fullHeight?: boolean; // New prop for full-height bottom sheet on mobile
  enableSwipeToDismiss?: boolean; // New prop to enable swipe-to-dismiss
  onOpenChange?: (open: boolean) => void; // For swipe-to-dismiss functionality
  swipeTopZoneHeight?: number; // Height of the top zone where swipe-to-dismiss is allowed
}

interface MobileDialogHeaderProps {
  className?: string;
  children: React.ReactNode;
}

interface MobileDialogFooterProps {
  className?: string;
  children: React.ReactNode;
}

interface MobileDialogTitleProps {
  className?: string;
  children: React.ReactNode;
}

interface MobileDialogDescriptionProps {
  className?: string;
  children: React.ReactNode;
}

const MobileDialog = ({ open, onOpenChange, children }: MobileDialogProps) => {
  const isDesktop = useMediaQuery("(min-width: 768px)");

  // Prevent body scroll when modal is open on mobile
  React.useEffect(() => {
    if (!isDesktop && open) {
      // Store the original overflow value from the style attribute, not computed style
      const originalOverflow = document.body.style.overflow;
      document.body.style.overflow = 'hidden';

      return () => {
        // Restore the original overflow value, or remove the style if it wasn't set
        if (originalOverflow) {
          document.body.style.overflow = originalOverflow;
        } else {
          document.body.style.removeProperty('overflow');
        }
      };
    }
  }, [open, isDesktop]);

  if (isDesktop) {
    return (
      <MobileDialogContext.Provider value={{ onOpenChange }}>
        <Dialog open={open} onOpenChange={onOpenChange}>
          {children}
        </Dialog>
      </MobileDialogContext.Provider>
    );
  }

  return (
    <MobileDialogContext.Provider value={{ onOpenChange }}>
      <Sheet open={open} onOpenChange={onOpenChange}>
        {children}
      </Sheet>
    </MobileDialogContext.Provider>
  );
};

const MobileDialogContent = React.forwardRef<HTMLDivElement, MobileDialogContentProps>(({
  className,
  children,
  fullHeight = false,
  enableSwipeToDismiss = true,
  onOpenChange: propOnOpenChange,
  swipeTopZoneHeight = 80,
  ...props
}, externalRef) => {
  const isDesktop = useMediaQuery("(min-width: 768px)");
  const context = React.useContext(MobileDialogContext);

  // Use prop onOpenChange if provided, otherwise use context
  const onOpenChange = propOnOpenChange || context.onOpenChange;

  // Swipe-to-dismiss functionality for mobile
  const { containerRef: swipeToDismissRef, dragOffset, isDragging } = useSwipeToDismiss({
    onDismiss: () => onOpenChange?.(false),
    threshold: 100,
    enabled: enableSwipeToDismiss && !isDesktop,
    topZoneHeight: swipeTopZoneHeight,
  });

  // Combine refs - use external ref if provided, otherwise use swipe-to-dismiss ref
  const combinedRef = React.useCallback((node: HTMLDivElement | null) => {
    // Set the swipe-to-dismiss ref
    if (swipeToDismissRef.current !== node) {
      swipeToDismissRef.current = node;
    }

    // Set the external ref if provided
    if (externalRef) {
      if (typeof externalRef === 'function') {
        externalRef(node);
      } else {
        externalRef.current = node;
      }
    }
  }, [externalRef]);

  if (isDesktop) {
    return (
      <DialogContent className={className} {...props}>
        {children}
      </DialogContent>
    );
  }

  // Full-height bottom sheet styling for mobile - keep modal position fixed
  const fullHeightStyles = fullHeight ? {
    // Full height modal stays exactly where it is, keyboard overlays naturally
    height: '100dvh',
    maxHeight: '100dvh',
    borderRadius: '0',
  } : {
    // Compact modal behavior
    maxHeight: '70dvh',
  };

  return (
    <SheetPrimitive.Portal>
      <SheetPrimitive.Overlay
        className={cn(
          "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-[60] bg-black/50"
        )}
      />
      <motion.div
        ref={combinedRef}
        animate={{
          y: dragOffset,
          opacity: isDragging ? Math.max(0.5, 1 - dragOffset / 200) : 1,
        }}
        transition={{
          type: "spring",
          damping: isDragging ? 50 : 25,
          stiffness: isDragging ? 300 : 200,
          duration: isDragging ? 0 : 0.3,
        }}
        className="fixed inset-x-0 bottom-0 z-[70]"
        style={{
          touchAction: enableSwipeToDismiss ? 'none' : 'auto',
        }}
      >
        <SheetPrimitive.Content
          className={cn(
            "bg-background data-[state=open]:animate-in data-[state=closed]:animate-out flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500 data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom w-full h-auto border-t min-h-0 overflow-y-auto overflow-x-hidden safe-area-inset-bottom",
            fullHeight ? "rounded-none" : "rounded-t-lg max-h-[70dvh]",
            className
          )}
          style={fullHeightStyles}
          {...props}
        >
          {/* Drag indicator for swipe-to-dismiss - top zone */}
          {enableSwipeToDismiss && (
            <div className="flex justify-center pt-3 pb-2 px-4">
              <div className="w-10 h-1.5 bg-muted-foreground/40 rounded-full" />
            </div>
          )}
          {children}
        </SheetPrimitive.Content>
      </motion.div>
    </SheetPrimitive.Portal>
  );
});

MobileDialogContent.displayName = "MobileDialogContent";

const MobileDialogHeader = ({ className, children, ...props }: MobileDialogHeaderProps) => {
  const isDesktop = useMediaQuery("(min-width: 768px)");

  if (isDesktop) {
    return (
      <DialogHeader className={className} {...props}>
        {children}
      </DialogHeader>
    );
  }

  return (
    <SheetHeader className={className} {...props}>
      {children}
    </SheetHeader>
  );
};

const MobileDialogFooter = ({ className, children, ...props }: MobileDialogFooterProps) => {
  const isDesktop = useMediaQuery("(min-width: 768px)");

  if (isDesktop) {
    return (
      <DialogFooter className={className} {...props}>
        {children}
      </DialogFooter>
    );
  }

  return (
    <SheetFooter className={className} {...props}>
      {children}
    </SheetFooter>
  );
};

const MobileDialogTitle = ({ className, children, ...props }: MobileDialogTitleProps) => {
  const isDesktop = useMediaQuery("(min-width: 768px)");

  if (isDesktop) {
    return (
      <DialogTitle className={className} {...props}>
        {children}
      </DialogTitle>
    );
  }

  return (
    <SheetTitle className={className} {...props}>
      {children}
    </SheetTitle>
  );
};

const MobileDialogDescription = ({ className, children, ...props }: MobileDialogDescriptionProps) => {
  const isDesktop = useMediaQuery("(min-width: 768px)");

  if (isDesktop) {
    return (
      <DialogDescription className={className} {...props}>
        {children}
      </DialogDescription>
    );
  }

  return (
    <SheetDescription className={className} {...props}>
      {children}
    </SheetDescription>
  );
};

export {
  MobileDialog,
  MobileDialogContent,
  MobileDialogHeader,
  MobileDialogFooter,
  MobileDialogTitle,
  MobileDialogDescription,
};
