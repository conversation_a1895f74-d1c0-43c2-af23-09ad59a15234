"use client";

import { useRef, useEffect, useState } from "react";
import { useMediaQuery } from "./use-media-query";

interface SwipeToDismissOptions {
  onDismiss: () => void;
  threshold?: number;
  enabled?: boolean;
  topZoneHeight?: number; // Height in pixels from the top where swipe-to-dismiss is allowed
}

interface TouchData {
  startX: number;
  startY: number;
  startTime: number;
  currentX: number;
  currentY: number;
  isInTopZone: boolean; // Track if the touch started in the top zone
}

export function useSwipeToDismiss({
  onDismiss,
  threshold = 100,
  enabled = true,
  topZoneHeight = 80, // Default to 80px from the top
}: SwipeToDismissOptions) {
  const isMobile = useMediaQuery("(max-width: 767px)");
  const touchDataRef = useRef<TouchData | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [dragOffset, setDragOffset] = useState(0);
  const [isDragging, setIsDragging] = useState(false);

  // Stable reference to onDismiss to prevent unnecessary re-renders
  const onDismissRef = useRef(onDismiss);
  onDismissRef.current = onDismiss;



  // Attach event listeners - use document-level listeners for better reliability
  useEffect(() => {
    if (!isMobile || !enabled) {
      return;
    }

    // Create stable event handlers that check if the touch is on our modal
    const onTouchStart = (e: TouchEvent) => {
      const container = containerRef.current;
      if (!container || !isMobile || !enabled) return;

      // Check if the touch is within our modal
      const target = e.target as Element;
      if (!container.contains(target)) return;

      const touch = e.touches[0];
      const containerRect = container.getBoundingClientRect();

      // Check if the touch started within the top zone
      const touchY = touch.clientY;
      const containerTop = containerRect.top;
      const isInTopZone = touchY <= containerTop + topZoneHeight;

      touchDataRef.current = {
        startX: touch.clientX,
        startY: touch.clientY,
        startTime: Date.now(),
        currentX: touch.clientX,
        currentY: touch.clientY,
        isInTopZone,
      };
      setIsDragging(false);
      setDragOffset(0);
    };

    const onTouchMove = (e: TouchEvent) => {
      const container = containerRef.current;
      if (!container || !isMobile || !enabled || !touchDataRef.current) return;

      const touch = e.touches[0];
      touchDataRef.current.currentX = touch.clientX;
      touchDataRef.current.currentY = touch.clientY;

      const deltaX = Math.abs(touch.clientX - touchDataRef.current.startX);
      const deltaY = touch.clientY - touchDataRef.current.startY;

      // Only start swipe-to-dismiss if we're moving down significantly AND started in the top zone
      if (deltaY > 0 && deltaY > deltaX && deltaY > 10 && touchDataRef.current.isInTopZone) {
        // Check if we're in a scrollable element that can still scroll up
        const target = e.target as Element;
        let currentElement: Element | null = target;
        let isInScrollableArea = false;

        while (currentElement && currentElement !== container) {
          const computedStyle = window.getComputedStyle(currentElement);
          const touchAction = computedStyle.touchAction;

          // Check if this is a scrollable element
          if (touchAction.includes('pan-y') ||
              (computedStyle.overflowY === 'auto' || computedStyle.overflowY === 'scroll')) {
            // Check if the element can still scroll up (meaning we shouldn't dismiss)
            if (currentElement.scrollTop > 0) {
              isInScrollableArea = true;
              break;
            }
          }

          currentElement = currentElement.parentElement;
        }

        // Only allow swipe-to-dismiss if we're not in a scrollable area that can scroll up
        if (!isInScrollableArea) {
          setIsDragging(true);
          setDragOffset(Math.min(deltaY, threshold * 2));
          e.preventDefault();
          e.stopPropagation();
        }
      }
    };

    const onTouchEnd = (e: TouchEvent) => {
      const container = containerRef.current;
      if (!container || !isMobile || !enabled || !touchDataRef.current) return;

      const touchData = touchDataRef.current;
      const deltaX = Math.abs(touchData.currentX - touchData.startX);
      const deltaY = touchData.currentY - touchData.startY;
      const deltaTime = Date.now() - touchData.startTime;


      touchDataRef.current = null;

      const velocity = deltaY / deltaTime;
      if (
        deltaY > 0 &&
        deltaY > deltaX &&
        deltaY > threshold &&
        deltaTime < 1000 &&
        velocity > 0.1
      ) {
        onDismissRef.current();
      }

      setIsDragging(false);
      setDragOffset(0);
    };

    // Attach to document to ensure we always get events
    document.addEventListener("touchstart", onTouchStart, { passive: false });
    document.addEventListener("touchmove", onTouchMove, { passive: false });
    document.addEventListener("touchend", onTouchEnd, { passive: false });

    return () => {
      document.removeEventListener("touchstart", onTouchStart);
      document.removeEventListener("touchmove", onTouchMove);
      document.removeEventListener("touchend", onTouchEnd);
    };
  }, [isMobile, enabled, threshold, topZoneHeight]);

  return {
    containerRef,
    dragOffset,
    isDragging,
    isMobile,
  };
}
